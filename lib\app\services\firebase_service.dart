import 'package:get/get.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/message_model.dart';
import '../models/user_model.dart';
import 'auth_service.dart';
import 'database_service.dart';

class FirebaseService extends GetxService {
  late FirebaseMessaging _messaging;
  late AuthService _authService;
  late DatabaseService _databaseService;
  
  String? _deviceToken;
  String? get deviceToken => _deviceToken;
  
  Future<FirebaseService> init() async {
    _messaging = FirebaseMessaging.instance;
    _authService = Get.find<AuthService>();
    _databaseService = Get.find<DatabaseService>();
    
    await _initializeMessaging();
    return this;
  }
  
  Future<void> _initializeMessaging() async {
    // Request permission for notifications
    NotificationSettings settings = await _messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('User granted permission');
    } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
      print('User granted provisional permission');
    } else {
      print('User declined or has not accepted permission');
    }
    
    // Get the device token
    _deviceToken = await _messaging.getToken();
    print('Device Token: $_deviceToken');
    
    // Update user's device token
    if (_deviceToken != null && _authService.currentUser != null) {
      await _authService.updateDeviceToken(_deviceToken!);
    }
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    
    // Handle notification taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
    
    // Handle initial message if app was opened from notification
    RemoteMessage? initialMessage = await _messaging.getInitialMessage();
    if (initialMessage != null) {
      _handleNotificationTap(initialMessage);
    }
  }
  
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('Received foreground message: ${message.messageId}');
    await _processIncomingMessage(message);
  }
  
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    print('Received background message: ${message.messageId}');
    // Handle background message processing here
  }
  
  Future<void> _handleNotificationTap(RemoteMessage message) async {
    print('Notification tapped: ${message.messageId}');
    // Navigate to chat screen or handle notification tap
    if (message.data.containsKey('senderId')) {
      // Navigate to chat with sender
      Get.toNamed('/chat', arguments: {
        'userId': message.data['senderId'],
        'userName': message.data['senderName'] ?? 'Unknown',
      });
    }
  }
  
  Future<void> _processIncomingMessage(RemoteMessage message) async {
    try {
      final data = message.data;
      
      if (data.containsKey('messageContent') && 
          data.containsKey('senderId') && 
          data.containsKey('receiverId')) {
        
        final messageModel = MessageModel(
          id: data['messageId'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
          senderId: data['senderId'],
          receiverId: data['receiverId'],
          content: data['messageContent'],
          timestamp: DateTime.now(),
          status: MessageStatus.delivered,
        );
        
        // Save message to local database
        await _databaseService.insertMessage(messageModel);
        
        // Notify UI about new message
        Get.find<DatabaseService>(); // This will trigger UI updates through GetX
      }
    } catch (e) {
      print('Error processing incoming message: $e');
    }
  }
  
  Future<bool> sendPushNotification({
    required String targetToken,
    required String senderName,
    required String messageContent,
    required String senderId,
    required String receiverId,
    String? messageId,
  }) async {
    try {
      // In a real app, you would send this through your backend server
      // For demo purposes, we'll simulate the notification
      
      final notification = {
        'to': targetToken,
        'notification': {
          'title': senderName,
          'body': messageContent,
          'sound': 'default',
        },
        'data': {
          'messageId': messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
          'senderId': senderId,
          'receiverId': receiverId,
          'senderName': senderName,
          'messageContent': messageContent,
          'type': 'chat_message',
        },
        'priority': 'high',
      };
      
      // Note: In production, you would need to implement a backend service
      // to send FCM messages using the Firebase Admin SDK
      print('Would send notification: ${jsonEncode(notification)}');
      
      return true;
    } catch (e) {
      print('Error sending push notification: $e');
      return false;
    }
  }
  
  Future<void> subscribeToTopic(String topic) async {
    await _messaging.subscribeToTopic(topic);
  }
  
  Future<void> unsubscribeFromTopic(String topic) async {
    await _messaging.unsubscribeFromTopic(topic);
  }
  
  Future<String?> getDeviceToken() async {
    return await _messaging.getToken();
  }
  
  Future<void> refreshToken() async {
    _deviceToken = await _messaging.getToken();
    if (_deviceToken != null && _authService.currentUser != null) {
      await _authService.updateDeviceToken(_deviceToken!);
    }
  }
  
  // Listen to token refresh
  void listenToTokenRefresh() {
    _messaging.onTokenRefresh.listen((newToken) {
      _deviceToken = newToken;
      if (_authService.currentUser != null) {
        _authService.updateDeviceToken(newToken);
      }
    });
  }
}
