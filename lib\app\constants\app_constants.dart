class AppConstants {
  // App Info
  static const String appName = 'ChatApp';
  static const String appVersion = '1.0.0';
  
  // API URLs
  static const String countriesApiUrl = 'https://restcountries.com/v3.1/all';
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String languageKey = 'language';
  static const String fcmTokenKey = 'fcm_token';
  static const String isLoggedInKey = 'is_logged_in';
  
  // Database
  static const String databaseName = 'chat_app.db';
  static const int databaseVersion = 1;
  
  // Tables
  static const String usersTable = 'users';
  static const String messagesTable = 'messages';
  static const String conversationsTable = 'conversations';
  
  // Firebase
  static const String firebaseTopicPrefix = 'user_';
  
  // Map Constants
  static const double mapZoom = 15.0;
  static const double rectangleRadius = 2000.0; // 2km in meters
  
  // Validation
  static const int minPasswordLength = 6;
  static const int maxMessageLength = 500;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
}
