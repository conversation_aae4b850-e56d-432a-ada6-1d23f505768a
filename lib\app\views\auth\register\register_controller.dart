import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../services/auth_service.dart';
import '../../../services/country_service.dart';
import '../../../models/country_model.dart';
import '../../../routes/app_routes.dart';

class RegisterController extends GetxController {
  final emailController = TextEditingController();
  final displayNameController = TextEditingController();
  final mobileController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  
  final RxBool isLoading = false.obs;
  final RxBool obscurePassword = true.obs;
  final RxBool obscureConfirmPassword = true.obs;
  final Rx<CountryModel?> selectedCountry = Rx<CountryModel?>(null);
  
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  
  late AuthService _authService;
  late CountryService _countryService;
  
  @override
  void onInit() {
    super.onInit();
    _authService = Get.find<AuthService>();
    _countryService = Get.find<CountryService>();
    _loadCountries();
  }
  
  @override
  void onClose() {
    emailController.dispose();
    displayNameController.dispose();
    mobileController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }
  
  Future<void> _loadCountries() async {
    await _countryService.fetchCountries();
  }
  
  void togglePasswordVisibility() {
    obscurePassword.value = !obscurePassword.value;
  }
  
  void toggleConfirmPasswordVisibility() {
    obscureConfirmPassword.value = !obscureConfirmPassword.value;
  }
  
  void selectCountry(CountryModel country) {
    selectedCountry.value = country;
  }
  
  void showCountryPicker() {
    Get.bottomSheet(
      Container(
        height: Get.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Text(
                'country'.tr,
                style: Get.textTheme.titleLarge,
              ),
            ),
            const Divider(),
            Expanded(
              child: Obx(() {
                if (_countryService.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }
                
                return ListView.builder(
                  itemCount: _countryService.countries.length,
                  itemBuilder: (context, index) {
                    final country = _countryService.countries[index];
                    return ListTile(
                      leading: Text(
                        country.flag,
                        style: const TextStyle(fontSize: 24),
                      ),
                      title: Text(country.name),
                      subtitle: Text(country.dialCode),
                      onTap: () {
                        selectCountry(country);
                        Get.back();
                      },
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
  
  Future<void> register() async {
    if (!formKey.currentState!.validate()) {
      return;
    }
    
    if (selectedCountry.value == null) {
      Get.snackbar(
        'error'.tr,
        'country_required'.tr,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }
    
    try {
      isLoading.value = true;
      
      final success = await _authService.register(
        email: emailController.text.trim(),
        displayName: displayNameController.text.trim(),
        country: selectedCountry.value!.name,
        mobile: mobileController.text.trim(),
        password: passwordController.text,
        confirmPassword: confirmPasswordController.text,
      );
      
      if (success) {
        Get.snackbar(
          'success'.tr,
          'registration_success'.tr,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        Get.offAllNamed(Routes.HOME);
      } else {
        Get.snackbar(
          'error'.tr,
          'registration_failed'.tr,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'error'.tr,
        'registration_failed'.tr,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  void goToLogin() {
    Get.back();
  }
  
  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'email_required'.tr;
    }
    if (!_authService.isValidEmail(value)) {
      return 'email_invalid'.tr;
    }
    return null;
  }
  
  String? validateDisplayName(String? value) {
    if (value == null || value.isEmpty) {
      return 'display_name_required'.tr;
    }
    return null;
  }
  
  String? validateMobile(String? value) {
    if (value == null || value.isEmpty) {
      return 'mobile_required'.tr;
    }
    if (!_authService.isValidMobile(value)) {
      return 'mobile_invalid'.tr;
    }
    return null;
  }
  
  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'password_required'.tr;
    }
    if (!_authService.isValidPassword(value)) {
      return 'password_min_length'.tr;
    }
    return null;
  }
  
  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'password_required'.tr;
    }
    if (value != passwordController.text) {
      return 'passwords_dont_match'.tr;
    }
    return null;
  }
}
