import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../models/message_model.dart';

class DatabaseService extends GetxService {
  static Database? _database;
  
  Database get database => _database!;
  
  Future<DatabaseService> init() async {
    await _initDatabase();
    return this;
  }
  
  Future<void> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'chat_app.db');
    
    _database = await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }
  
  Future<void> _onCreate(Database db, int version) async {
    // Create users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        display_name TEXT NOT NULL,
        country TEXT NOT NULL,
        mobile TEXT NOT NULL,
        fcm_token TEXT,
        created_at INTEGER,
        updated_at INTEGER
      )
    ''');

    // Create messages table
    await db.execute('''
      CREATE TABLE messages (
        id TEXT PRIMARY KEY,
        sender_id TEXT NOT NULL,
        receiver_id TEXT NOT NULL,
        content TEXT NOT NULL,
        type TEXT DEFAULT 'text',
        status TEXT DEFAULT 'sent',
        timestamp INTEGER NOT NULL,
        delivered_at INTEGER,
        read_at INTEGER,
        is_from_current_user INTEGER DEFAULT 0
      )
    ''');

    // Create chat_rooms table for better organization
    await db.execute('''
      CREATE TABLE chat_rooms (
        id TEXT PRIMARY KEY,
        participant1Id TEXT NOT NULL,
        participant2Id TEXT NOT NULL,
        lastMessageId TEXT,
        lastMessageTime INTEGER,
        createdAt INTEGER NOT NULL,
        UNIQUE(participant1Id, participant2Id)
      )
    ''');
  }
  
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
  }
  
  // User operations
  Future<int> insertUser(UserModel user) async {
    return await database.insert('users', user.toDatabase());
  }

  Future<UserModel?> getUserById(String id) async {
    final List<Map<String, dynamic>> maps = await database.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return UserModel.fromDatabase(maps.first);
    }
    return null;
  }

  Future<UserModel?> getUserByEmail(String email) async {
    final List<Map<String, dynamic>> maps = await database.query(
      'users',
      where: 'email = ?',
      whereArgs: [email],
    );

    if (maps.isNotEmpty) {
      return UserModel.fromDatabase(maps.first);
    }
    return null;
  }

  Future<int> updateUser(UserModel user) async {
    return await database.update(
      'users',
      user.toDatabase(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }
  
  // Message operations
  Future<int> insertMessage(MessageModel message) async {
    return await database.insert('messages', message.toDatabase());
  }

  Future<List<MessageModel>> getMessages(String userId1, String userId2) async {
    final List<Map<String, dynamic>> maps = await database.query(
      'messages',
      where: '(sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)',
      whereArgs: [userId1, userId2, userId2, userId1],
      orderBy: 'timestamp ASC',
    );

    return List.generate(maps.length, (i) {
      return MessageModel.fromDatabase(maps[i]);
    });
  }
  
  Future<int> markMessageAsRead(String messageId) async {
    return await database.update(
      'messages',
      {'status': 'read', 'read_at': DateTime.now().millisecondsSinceEpoch},
      where: 'id = ?',
      whereArgs: [messageId],
    );
  }
  
  Future<int> deleteMessage(String messageId) async {
    return await database.delete(
      'messages',
      where: 'id = ?',
      whereArgs: [messageId],
    );
  }
  
  // Chat room operations
  Future<int> createChatRoom(String participant1Id, String participant2Id) async {
    final chatRoom = {
      'id': '${participant1Id}_$participant2Id',
      'participant1Id': participant1Id,
      'participant2Id': participant2Id,
      'createdAt': DateTime.now().millisecondsSinceEpoch,
    };
    
    return await database.insert('chat_rooms', chatRoom);
  }
  
  Future<List<Map<String, dynamic>>> getChatRooms(String userId) async {
    return await database.query(
      'chat_rooms',
      where: 'participant1Id = ? OR participant2Id = ?',
      whereArgs: [userId, userId],
      orderBy: 'lastMessageTime DESC',
    );
  }
  
  Future<void> close() async {
    await _database?.close();
  }
}
